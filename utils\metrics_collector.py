"""
LiveKit Agents Metrics Collection Utilities

This module provides utilities for collecting, storing, and analyzing
LiveKit Agents metrics as described in the LiveKit documentation.
"""

import logging
import asyncio
from typing import Optional, Dict, Any
from livekit.agents import metrics, MetricsCollectedEvent
from models.metrics import (
    STTMetrics, LLMMetrics, TTSMetrics, EOUMetrics, 
    ConversationLatencyMetrics, AgentSession, UsageSummary
)

logger = logging.getLogger(__name__)


class DatabaseMetricsCollector:
    """
    Collects and stores LiveKit Agents metrics to database.
    
    Usage:
        collector = DatabaseMetricsCollector(session_id="your-session-id")
        
        @session.on("metrics_collected")
        def _on_metrics_collected(ev: MetricsCollectedEvent):
            await collector.collect_and_store(ev.metrics)
    """
    
    def __init__(self, session_id: Optional[str] = None):
        self.session_id = session_id
        self.usage_collector = metrics.UsageCollector()
        
    async def collect_and_store(self, metric: Any) -> None:
        """
        Collect metrics and store them in the database.
        
        Args:
            metric: The metrics object from LiveKit Agents
        """
        try:
            # Log the metrics using LiveKit's built-in formatter
            metrics.log_metrics(metric)
            
            # Collect for usage summary
            self.usage_collector.collect(metric)
            
            # Store in database based on metric type
            if isinstance(metric, metrics.STTMetrics):
                await self._store_stt_metrics(metric)
            elif isinstance(metric, metrics.LLMMetrics):
                await self._store_llm_metrics(metric)
            elif isinstance(metric, metrics.TTSMetrics):
                await self._store_tts_metrics(metric)
            elif isinstance(metric, metrics.EOUMetrics):
                await self._store_eou_metrics(metric)

            await self._try_calculate_conversation_latency(getattr(metric, 'speech_id', None))
                
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
    
    async def _store_stt_metrics(self, metric: metrics.STTMetrics) -> STTMetrics:
        """Store STT metrics to database"""
        return await STTMetrics.create(
            audio_duration=metric.audio_duration,
            duration=metric.duration,
            streamed=metric.streamed,
            speech_id=getattr(metric, 'speech_id', None),
            session_id=self.session_id
        )
    
    async def _store_llm_metrics(self, metric: metrics.LLMMetrics) -> LLMMetrics:
        """Store LLM metrics to database"""
        return await LLMMetrics.create(
            duration=metric.duration,
            completion_tokens=metric.completion_tokens,
            prompt_tokens=metric.prompt_tokens,
            prompt_cached_tokens=getattr(metric, 'prompt_cached_tokens', 0),
            total_tokens=metric.total_tokens,
            tokens_per_second=getattr(metric, 'tokens_per_second', None),
            ttft=metric.ttft,
            speech_id=metric.speech_id,
            session_id=self.session_id
        )
    
    async def _store_tts_metrics(self, metric: metrics.TTSMetrics) -> TTSMetrics:
        """Store TTS metrics to database"""
        return await TTSMetrics.create(
            audio_duration=metric.audio_duration,
            characters_count=metric.characters_count,
            duration=metric.duration,
            ttfb=metric.ttfb,
            speech_id=metric.speech_id,
            streamed=metric.streamed,
            session_id=self.session_id
        )
    
    async def _store_eou_metrics(self, metric: metrics.EOUMetrics) -> EOUMetrics:
        """Store EOU metrics to database"""
        eou_record = await EOUMetrics.create(
            end_of_utterance_delay=metric.end_of_utterance_delay,
            transcription_delay=metric.transcription_delay,
            on_user_turn_completed_delay=getattr(metric, 'on_user_turn_completed_delay', None),
            speech_id=metric.speech_id,
            session_id=self.session_id
        )
                
        return eou_record
    
    async def _try_calculate_conversation_latency(self, speech_id: str) -> Optional[ConversationLatencyMetrics]:
        """
        Attempt to calculate conversation latency if all required metrics are available.
        
        Args:
            speech_id: The speech ID to calculate latency for
            
        Returns:
            ConversationLatencyMetrics if successful, None otherwise
        """
        try:
            # First, check if all required metrics exist
            eou = await EOUMetrics.filter(speech_id=speech_id).first()
            llm = await LLMMetrics.filter(speech_id=speech_id).first()
            tts = await TTSMetrics.filter(speech_id=speech_id).first()
            
            if not all([eou, llm, tts]):
                logger.debug(f"Missing required metrics for conversation latency calculation for speech_id {speech_id}")
                return None
                
            # Now calculate and store the latency
            latency = await ConversationLatencyMetrics.calculate_and_store(
                speech_id=speech_id, 
                session_id=self.session_id
            )
            
            if latency:
                logger.info(f"Calculated conversation latency for speech_id {speech_id}: {latency.total_latency}s")
            
            return latency
        except Exception as e:
            logger.error(f"Could not calculate conversation latency for speech_id {speech_id}: {e}")
            return None
    
    async def get_usage_summary(self) -> Dict[str, Any]:
        """Get usage summary from the usage collector"""
        return self.usage_collector.get_summary()
    
    async def generate_session_summary(self) -> Optional[UsageSummary]:
        """Generate and store usage summary for the current session"""
        if not self.session_id:
            logger.warning("No session_id provided, cannot generate session summary")
            return None
            
        try:
            return await UsageSummary.generate_for_session(self.session_id)
        except Exception as e:
            logger.error(f"Error generating session summary: {e}")
            return None


class MetricsAnalyzer:
    """
    Utility class for analyzing stored metrics data.
    """
    
    @staticmethod
    async def get_session_performance(session_id: str) -> Dict[str, Any]:
        """
        Get comprehensive performance metrics for a session.
        
        Args:
            session_id: The session ID to analyze
            
        Returns:
            Dictionary containing performance metrics
        """
        try:
            session = await AgentSession.get(id=session_id)
            
            # Get average latency
            avg_latency = await session.get_average_latency()
            
            # Get token usage
            token_usage = await session.get_total_tokens_used()
            
            # Get conversation turns
            llm_metrics = await session.llm_metrics.all()
            unique_speech_ids = set(m.speech_id for m in llm_metrics if m.speech_id)
            total_turns = len(unique_speech_ids)
            
            # Get TTS character count
            tts_metrics = await session.tts_metrics.all()
            total_tts_chars = sum(m.characters_count or 0 for m in tts_metrics)
            
            # Get STT audio duration
            stt_metrics = await session.stt_metrics.all()
            total_stt_duration = sum(m.audio_duration or 0 for m in stt_metrics)
            
            return {
                "session_id": session_id,
                "session_name": session.session_name,
                "started_at": session.started_at,
                "ended_at": session.ended_at,
                "duration_minutes": session.duration_minutes,
                "average_latency_seconds": avg_latency,
                "total_conversation_turns": total_turns,
                "token_usage": token_usage,
                "total_tts_characters": total_tts_chars,
                "total_stt_audio_duration_seconds": float(total_stt_duration),
                "status": session.status
            }
            
        except Exception as e:
            logger.error(f"Error analyzing session {session_id}: {e}")
            return {}
    
    @staticmethod
    async def get_latency_breakdown(speech_id: str) -> Dict[str, Any]:
        """
        Get detailed latency breakdown for a specific conversation turn.
        
        Args:
            speech_id: The speech ID to analyze
            
        Returns:
            Dictionary containing latency breakdown
        """
        try:
            # Get all metrics for this speech_id
            eou = await EOUMetrics.filter(speech_id=speech_id).first()
            llm = await LLMMetrics.filter(speech_id=speech_id).first()
            tts = await TTSMetrics.filter(speech_id=speech_id).first()
            latency = await ConversationLatencyMetrics.filter(speech_id=speech_id).first()
            
            return {
                "speech_id": speech_id,
                "end_of_utterance_delay": float(eou.end_of_utterance_delay) if eou and eou.end_of_utterance_delay else None,
                "transcription_delay": float(eou.transcription_delay) if eou and eou.transcription_delay else None,
                "llm_ttft": float(llm.ttft) if llm and llm.ttft else None,
                "llm_duration": float(llm.duration) if llm and llm.duration else None,
                "tts_ttfb": float(tts.ttfb) if tts and tts.ttfb else None,
                "tts_duration": float(tts.duration) if tts and tts.duration else None,
                "total_latency": float(latency.total_latency) if latency and latency.total_latency else None,
                "components_available": {
                    "eou": eou is not None,
                    "llm": llm is not None,
                    "tts": tts is not None
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting latency breakdown for speech_id {speech_id}: {e}")
            return {}


# Example usage functions for integration
async def setup_metrics_collection(session, session_id: str) -> DatabaseMetricsCollector:
    """
    Set up metrics collection for a LiveKit Agents session.
    
    Args:
        session: The LiveKit Agents session object
        session_id: Unique identifier for the session
        
    Returns:
        DatabaseMetricsCollector instance
    """
    collector = DatabaseMetricsCollector(session_id=session_id)
    
    # Create a synchronous wrapper that launches the async function as a task
    def _on_metrics_collected(ev: MetricsCollectedEvent):
        # Create an asyncio task to run the async function
        asyncio.create_task(collector.collect_and_store(ev.metrics))
    
    # Register the synchronous wrapper
    session.on("metrics_collected", _on_metrics_collected)
    
    return collector


async def log_session_usage(collector: DatabaseMetricsCollector):
    """
    Log usage summary for a session.
    
    Args:
        collector: The DatabaseMetricsCollector instance
    """
    try:
        summary = await collector.get_usage_summary()
        logger.info(f"Session Usage Summary: {summary}")
        
        # Also generate and store database summary
        db_summary = await collector.generate_session_summary()
        if db_summary:
            logger.info(f"Database summary generated: {db_summary}")
            
    except Exception as e:
        logger.error(f"Error logging session usage: {e}")

